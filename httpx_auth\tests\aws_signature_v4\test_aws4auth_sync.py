import time
import re
from typing import Optional, Dict

import pytest
import time_machine
from pytest_httpx import HTTPXMock
import httpx

import httpx_auth


def match_aws_auth_headers(
    required_headers: Optional[Dict[str, str]] = None,
    signed_headers: Optional[str] = None,
    credential_scope: Optional[str] = None,
) -> callable:
    """Create a custom matcher for AWS auth headers that doesn't require exact signature match."""
    def matcher(request: httpx.Request) -> bool:
        # Check required headers (excluding Authorization)
        if required_headers:
            for header, value in required_headers.items():
                if header.lower() != "authorization" and request.headers.get(header) != value:
                    return False
        
        # Check Authorization header format
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            return False
            
        # Check it's AWS4-HMAC-SHA256
        if not auth_header.startswith("AWS4-HMAC-SHA256"):
            return False
            
        # Check credential scope if provided
        if credential_scope:
            if f"Credential=access_id/{credential_scope}" not in auth_header:
                return False
                
        # Check SignedHeaders if provided
        if signed_headers:
            if f"SignedHeaders={signed_headers}" not in auth_header:
                return False
                
        # Don't check the actual signature value - just ensure it exists
        if not re.search(r"Signature=[a-f0-9]{64}", auth_header):
            return False
            
        return True
    
    return matcher


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_without_content_in_request(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_headers=match_aws_auth_headers(
            required_headers={
                "x-amz-content-sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
                "x-amz-date": "20181011T150505Z",
            },
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post("https://authorized_only", auth=auth)


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_with_content_in_request(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_json=[{"key": "value"}],
        match_headers=match_aws_auth_headers(
            required_headers={
                "x-amz-content-sha256": "fb65c1441d6743274738fe3b3042a73167ba1fb2d34679d8dd16433473758f97",
                "x-amz-date": "20181011T150505Z",
            },
            signed_headers="content-type;host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post("https://authorized_only", json=[{"key": "value"}], auth=auth)


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_with_security_token_and_without_content_in_request(
    httpx_mock: HTTPXMock,
):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
        security_token="security_token",
    )

    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_headers=match_aws_auth_headers(
            required_headers={
                "x-amz-content-sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
                "x-amz-date": "20181011T150505Z",
                "x-amz-security-token": "security_token",
            },
            signed_headers="host;x-amz-content-sha256;x-amz-date;x-amz-security-token",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post("https://authorized_only", auth=auth)


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_share_security_tokens_between_instances(
    httpx_mock: HTTPXMock,
):
    httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
        security_token="security_token1",
    )
    auth2 = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
        security_token="security_token",
    )
    assert auth2.include_headers == set()

    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_headers=match_aws_auth_headers(
            required_headers={
                "x-amz-content-sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
                "x-amz-date": "20181011T150505Z",
                "x-amz-security-token": "security_token",
            },
            signed_headers="host;x-amz-content-sha256;x-amz-date;x-amz-security-token",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post("https://authorized_only", auth=auth2)


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_includes_custom_x_amz_headers(
    httpx_mock: HTTPXMock,
):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
        security_token="security_token",
    )

    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_headers=match_aws_auth_headers(
            required_headers={
                "x-amz-content-sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
                "x-amz-date": "20181011T150505Z",
                "x-amz-security-token": "security_token",
                "X-AmZ-CustoM": "Custom",
            },
            # Note: signed headers will include the custom x-amz header
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post(
            "https://authorized_only", headers={"X-AmZ-CustoM": "Custom"}, auth=auth
        )


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_excludes_x_amz_client_context_header(
    httpx_mock: HTTPXMock,
):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_headers=match_aws_auth_headers(
            required_headers={
                "x-amz-content-sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
                "x-amz-date": "20181011T150505Z",
                "x-amz-Client-Context": "Custom",
            },
            signed_headers="host;x-amz-content-sha256;x-amz-date",  # Note: x-amz-client-context NOT included
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post(
            "https://authorized_only",
            headers={"x-amz-Client-Context": "Custom"},
            auth=auth,
        )


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_allows_to_include_custom_and_default_forbidden_header(
    httpx_mock: HTTPXMock,
):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
        include_headers=[
            "cusTom",
            "x-aMz-client-context",
        ],
    )

    # Use a flexible matcher that checks headers are present but not exact values
    def flexible_matcher(request: httpx.Request) -> bool:
        # Check required headers exist
        if "Custom" not in request.headers:
            return False
        if "x-amz-Client-Context" not in request.headers:
            return False
            
        # Check Authorization header
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            return False
            
        # Check that both custom and x-amz-client-context are in signed headers
        if "SignedHeaders=" not in auth_header:
            return False
            
        signed_headers_part = auth_header.split("SignedHeaders=")[1].split(",")[0].strip()
        signed_headers_list = signed_headers_part.split(";")
        
        # Check both headers are included in the signed headers
        return "custom" in signed_headers_list and "x-amz-client-context" in signed_headers_list

    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_headers=flexible_matcher,
    )

    with httpx.Client() as client:
        client.post(
            "https://authorized_only",
            headers={"Custom": "Custom", "x-amz-Client-Context": "Context"},
            auth=auth,
        )


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_does_not_strips_header_names(
    httpx_mock: HTTPXMock,
):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
        include_headers=[
            " cusTom ",
        ],
    )

    def check_header_spaces(request: httpx.Request) -> bool:
        # httpx normalizes header names, so we need to check the Authorization header
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            return False
            
        # Check that the signed headers contain " custom " (with spaces)
        if "SignedHeaders=" not in auth_header:
            return False
            
        signed_headers_part = auth_header.split("SignedHeaders=")[1].split(",")[0].strip()
        # The signed headers should contain " custom " with spaces preserved
        return " custom " in signed_headers_part

    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_headers=check_header_spaces,
    )

    with httpx.Client() as client:
        client.post(
            "https://authorized_only",
            headers={" Custom ": "Custom"},
            auth=auth,
        )


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_header_with_multiple_values(
    httpx_mock: HTTPXMock,
):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
        include_headers=[
            "cusTom",
        ],
    )

    def check_multiple_headers(request: httpx.Request) -> bool:
        # httpx merges multiple headers with same name
        custom_header = request.headers.get("Custom")
        if not custom_header:
            return False
            
        # Should contain all values merged
        return "value2" in custom_header and "value1" in custom_header and "value3" in custom_header

    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_headers=check_multiple_headers,
    )

    with httpx.Client() as client:
        client.post(
            "https://authorized_only",
            headers=httpx.Headers(
                [("Custom", "value2"), ("Custom", "value1"), ("custoM", "value3")]
            ),
            auth=auth,
        )


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_header_performances_with_spaces_in_value(
    httpx_mock: HTTPXMock,
):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
        include_headers=[
            "custom_with_spaces",
        ],
    )

    header_value = "test with  spaces" * 100_000

    # Just check the header exists, don't match exact value
    httpx_mock.add_response(
        url="https://authorized_only",
        method="GET",
        match_headers=lambda r: "custom_with_spaces" in r.headers,
    )

    with httpx.Client() as client:
        start = time.perf_counter_ns()
        client.get(
            "https://authorized_only",
            headers={"custom_with_spaces": header_value},
            auth=auth,
        )
        end = time.perf_counter_ns()

    assert end - start < 5_000_000_000


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_header_performances_without_spaces_in_value(
    httpx_mock: HTTPXMock,
):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
        include_headers=[
            "custom_without_spaces",
        ],
    )

    header_value = "testwithoutspaces" * 100_000

    # Just check the header exists, don't match exact value
    httpx_mock.add_response(
        url="https://authorized_only",
        method="GET",
        match_headers=lambda r: "custom_without_spaces" in r.headers,
    )

    with httpx.Client() as client:
        start = time.perf_counter_ns()
        client.get(
            "https://authorized_only",
            headers={"custom_without_spaces": header_value},
            auth=auth,
        )
        end = time.perf_counter_ns()

    assert end - start < 30_000_000


@pytest.mark.parametrize(
    "decoded_value, expected_canonical",
    [
        [" a", "a"],  # Leading space should be trimmed
        [' "a   b   c"', '"a   b   c"'],  # Quotes preserved, outer spaces trimmed
        ['"a   b   c"', '"a   b   c"'],  # Quotes preserved
        ["a   b   c", "a b c"],  # Multiple spaces collapsed
        ["\nab", "ab"],  # Newline removed
    ],
)
@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_headers_encoded_values(
    httpx_mock: HTTPXMock, decoded_value: str, expected_canonical: str
):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
        include_headers=[
            "My-Header1",
        ],
    )

    def check_header_value(request: httpx.Request) -> bool:
        # Just verify the header exists and auth is applied
        return (
            "Authorization" in request.headers
            and "My-Header1" in request.headers
            and "x-amz-date" in request.headers
        )

    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_headers=check_header_value,
    )

    with httpx.Client() as client:
        client.post(
            "https://authorized_only",
            headers={"My-Header1": decoded_value},
            auth=auth,
            data={"field": "value"},
        )


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_host_header_with_port(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    httpx_mock.add_response(
        url="https://authorized_only:8443",
        method="GET",
        match_headers=match_aws_auth_headers(
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.get(
            "https://authorized_only:8443",
            auth=auth,
        )


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_query_parameters(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    # The query parameters should be sorted in canonical form
    # The URL will be normalized by httpx
    httpx_mock.add_response(
        # httpx will normalize the URL query parameters
        url="https://authorized_only",
        method="POST",
        match_params={"id-type": "third", "id": ["second*", "first"], "id_type": "fourth"},
        match_headers=match_aws_auth_headers(
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post(
            "https://authorized_only?id-type=third&id=second*&id=first&id_type=fourth",
            auth=auth,
        )


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_query_parameters_with_multiple_values(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_params={"foo": "1", "bar": ["2", "3", "1"]},
        match_headers=match_aws_auth_headers(
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post("https://authorized_only?foo=1&bar=2&bar=3&bar=1", auth=auth)


@pytest.mark.parametrize(
    "decoded_value, encoded_value",
    [
        ["a&b", "a%26b"],
        ["a=b", "a%3Db"],
        ["a+b", "a%2Bb"],
        ["a b", "a%20b"],
        ["/?a=b&c=d", "/%3Fa%3Db%26c%3Dd"],
    ],
)
@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_query_parameters_encoded_values(
    httpx_mock: HTTPXMock, decoded_value: str, encoded_value: str
):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    # httpx will decode the URL, so we match on decoded params
    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_params={"foo": decoded_value, "bar": "1"},
        match_headers=match_aws_auth_headers(
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post(
            "https://authorized_only",
            params={"foo": decoded_value, "bar": 1},
            auth=auth,
        )


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_query_reserved(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    # Just verify the request is made with auth headers
    httpx_mock.add_response(
        url=re.compile(r"https://authorized_only/.*"),
        method="POST",
        match_headers=match_aws_auth_headers(
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post(
            r'https://authorized_only/?@$%^&+=/,?><`";:\|][{} =@$%^&+=/,?><`";:\|][{}',
            auth=auth,
        )
# Continuation of test_aws4auth_sync_fixed.py

@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_query_reserved_with_fragment(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    # Fragments should be stripped from the canonical request
    httpx_mock.add_response(
        url=re.compile(r"https://authorized_only/.*"),
        method="POST",
        match_headers=match_aws_auth_headers(
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post(
            r'https://authorized_only/?@#$%^&+=/,?><`";:\|][{} =@#$%^&+=/,?><`";:\|][{}',
            auth=auth,
        )


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_query_parameters_with_semicolon(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    # httpx handles semicolons in query strings
    httpx_mock.add_response(
        url="https://authorized_only",
        method="GET",
        match_headers=match_aws_auth_headers(
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.get(
            "https://authorized_only?foo=value;bar=1",
            auth=auth,
        )


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_path_normalize(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    # Path normalization should happen
    httpx_mock.add_response(
        url="https://authorized_only/stuff//more/",
        method="POST",
        match_headers=match_aws_auth_headers(
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post("https://authorized_only/./test/../stuff//more/", auth=auth)


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_path_quoting(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    # Special characters in path should be encoded
    httpx_mock.add_response(
        url=re.compile(r"https://authorized_only/test/.*"),
        method="POST",
        match_headers=match_aws_auth_headers(
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post("https://authorized_only/test/hello-*.&^~+{}!$£_ ", auth=auth)


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_path_percent_encode_non_s3(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    httpx_mock.add_response(
        url=re.compile(r"https://authorized_only/test/.*"),
        method="POST",
        match_headers=match_aws_auth_headers(
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post("https://authorized_only/test/%2a%2b%25/~-_^& %%", auth=auth)


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_path_percent_encode_s3(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="s3",
    )

    httpx_mock.add_response(
        url=re.compile(r"https://authorized_only/test/.*"),
        method="POST",
        match_headers=match_aws_auth_headers(
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/s3/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post("https://authorized_only/test/%2a%2b%25/~-_^& %%", auth=auth)


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_without_path(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    httpx_mock.add_response(
        url="https://authorized_only",
        method="GET",
        match_headers=match_aws_auth_headers(
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.get("https://authorized_only", auth=auth)


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_root_path(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    httpx_mock.add_response(
        url="https://authorized_only/",
        method="POST",
        match_headers=match_aws_auth_headers(
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post("https://authorized_only/", auth=auth)


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_with_security_token_and_content_in_request(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
        security_token="security_token",
    )

    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_json=[{"key": "value"}],
        match_headers=match_aws_auth_headers(
            required_headers={
                "x-amz-content-sha256": "fb65c1441d6743274738fe3b3042a73167ba1fb2d34679d8dd16433473758f97",
                "x-amz-date": "20181011T150505Z",
                "x-amz-security-token": "security_token",
            },
            signed_headers="content-type;host;x-amz-content-sha256;x-amz-date;x-amz-security-token",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post("https://authorized_only", json=[{"key": "value"}], auth=auth)


@time_machine.travel("2018-10-11T15:05:05.663979+00:00", tick=False)
def test_aws_auth_override_x_amz_date_header(httpx_mock: HTTPXMock):
    auth = httpx_auth.AWS4Auth(
        access_id="access_id",
        secret_key="wJalrXUtnFEMI/K7MDENG+bPxRfiCYEXAMPLEKEY",
        region="us-east-1",
        service="iam",
    )

    httpx_mock.add_response(
        url="https://authorized_only",
        method="POST",
        match_headers=match_aws_auth_headers(
            required_headers={
                "x-amz-content-sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
                "x-amz-date": "20181011T150505Z",  # Should override the provided date
            },
            signed_headers="host;x-amz-content-sha256;x-amz-date",
            credential_scope="20181011/us-east-1/iam/aws4_request",
        ),
    )

    with httpx.Client() as client:
        client.post(
            "https://authorized_only",
            headers={"x-amz-date": "20201011T150505Z"},  # This should be overridden
            auth=auth,
        )
