{"tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_encoded_values[/?a=b&c=d-/%3Fa%3Db%26c%3Dd-e57ce6433a7158380da02ee7afbcf7adca26e3b61ff46f80816453f932e67ccc]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_share_security_tokens_between_instances": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_encoded_values[a+b-a%2Bb-a69fde859c7ba14634f827bdbfce8e558709b26e5eceb2e507e99430ba8e79df]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_header_performances_without_spaces_in_value": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_encoded_values[a&b-a%26b-db0909a13cb56b574ea0c828a2875537a90ce7bed00e8237b817b4211adb8662]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_reserved": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_does_not_strips_header_names": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_headers_encoded_values[ a-92c77bd0e66ae6f12fa41491ebcb524127b2df9677fd7ccf9ffff698021e0b28]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_header_performances_with_spaces_in_value": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_allows_to_include_custom_and_default_forbidden_header": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_header_with_multiple_values": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_without_content_in_request": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_with_content_in_request": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_with_security_token_and_without_content_in_request": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_includes_custom_x_amz_headers": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_excludes_x_amz_client_context_header": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_headers_encoded_values[ a-a]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_headers_encoded_values[ \"a   b   c\"-\"a   b   c\"]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_headers_encoded_values[\"a   b   c\"-\"a   b   c\"]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_headers_encoded_values[a   b   c-a b c]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_headers_encoded_values[\\nab-ab]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_host_header_with_port": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_with_multiple_values": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_encoded_values[a&b-a%26b]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_encoded_values[a=b-a%3Db]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_encoded_values[a+b-a%2Bb]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_encoded_values[a b-a%20b]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_encoded_values[/?a=b&c=d-/%3Fa%3Db%26c%3Dd]": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_reserved_with_fragment": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_with_semicolon": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_path_normalize": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_path_quoting": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_path_percent_encode_non_s3": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_path_percent_encode_s3": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_without_path": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_root_path": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_with_security_token_and_content_in_request": true, "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_override_x_amz_date_header": true}