["tests/api_key/test_api_key.py::test_header_api_key_requires_an_api_key", "tests/api_key/test_api_key.py::test_query_api_key_requires_an_api_key", "tests/aws_signature_v4/test_aws4auth.py::test_aws_auth_with_empty_secret_key", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_allows_to_include_custom_and_default_forbidden_header", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_does_not_strips_header_names", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_excludes_x_amz_client_context_header", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_header_performances_with_spaces_in_value", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_header_performances_without_spaces_in_value", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_header_with_multiple_values", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_headers_encoded_values[ \"a   b   c\"-38fbdeb88fa3785191adc95113bcf665b4151cc2d2379e6a086bee9066f65a38]", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_headers_encoded_values[ a-92c77bd0e66ae6f12fa41491ebcb524127b2df9677fd7ccf9ffff698021e0b28]", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_headers_encoded_values[\"a   b   c\"-38fbdeb88fa3785191adc95113bcf665b4151cc2d2379e6a086bee9066f65a38]", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_headers_encoded_values[\\nab-3072938eb28cff19726cc2a27d5e570f916887a639b26475b390dd0edacf6496]", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_headers_encoded_values[a   b   c-7b6aea4a2378417c631c5621ddc99a94591022c775cfbb9dbf5c360492e238ef]", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_host_header_with_port", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_includes_custom_x_amz_headers", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_override_x_amz_date_header", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_path_normalize", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_path_percent_encode_non_s3", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_path_percent_encode_s3", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_path_quoting", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_encoded_values[/?a=b&c=d-/%3Fa%3Db%26c%3Dd-e57ce6433a7158380da02ee7afbcf7adca26e3b61ff46f80816453f932e67ccc]", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_encoded_values[a b-a%20b-6eef487def6c062806b89437027e12f641f35e1dfda5cc7ae49da777ad5f0fb4]", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_encoded_values[a&b-a%26b-db0909a13cb56b574ea0c828a2875537a90ce7bed00e8237b817b4211adb8662]", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_encoded_values[a+b-a%2Bb-a69fde859c7ba14634f827bdbfce8e558709b26e5eceb2e507e99430ba8e79df]", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_encoded_values[a=b-a%3Db-e4e0fb580cb9b304f6fb5f7e9294156fb1b17f0d23a79e6ccfa86ec8120a5c7e]", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_with_multiple_values", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_parameters_with_semicolon", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_reserved", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_query_reserved_with_fragment", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_root_path", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_share_security_tokens_between_instances", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_with_content_in_request", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_with_security_token_and_content_in_request", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_with_security_token_and_without_content_in_request", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_without_content_in_request", "tests/aws_signature_v4/test_aws4auth_sync.py::test_aws_auth_without_path", "tests/features/token_cache/test_json_token_file_cache.py::test_add_bearer_tokens", "tests/features/token_cache/test_json_token_file_cache.py::test_missing_token", "tests/features/token_cache/test_json_token_file_cache.py::test_missing_token_function", "tests/features/token_cache/test_json_token_file_cache.py::test_save_bearer_token_exception_handling", "tests/features/token_cache/test_json_token_file_cache.py::test_save_bearer_tokens", "tests/oauth2/authorization_code/okta/test_oauth2_authorization_code_okta.py::test_header_value_must_contains_token", "tests/oauth2/authorization_code/test_oauth2_authorization_code.py::test_authorization_url_is_mandatory", "tests/oauth2/authorization_code/test_oauth2_authorization_code.py::test_header_value_must_contains_token", "tests/oauth2/authorization_code/test_oauth2_authorization_code.py::test_token_url_is_mandatory", "tests/oauth2/authorization_code/wakatime/test_oauth2_authorization_code_wakatime.py::test_empty_scope_is_invalid", "tests/oauth2/authorization_code/wakatime/test_oauth2_authorization_code_wakatime.py::test_header_value_must_contains_token", "tests/oauth2/authorization_code/wakatime/test_oauth2_authorization_code_wakatime.py::test_scope_is_mandatory", "tests/oauth2/authorization_code_pkce/okta/test_oauth2_authorization_code_pkce_okta.py::test_header_value_must_contains_token", "tests/oauth2/authorization_code_pkce/test_oauth2_authorization_code_pkce.py::test_authorization_url_is_mandatory", "tests/oauth2/authorization_code_pkce/test_oauth2_authorization_code_pkce.py::test_header_value_must_contains_token", "tests/oauth2/authorization_code_pkce/test_oauth2_authorization_code_pkce.py::test_token_url_is_mandatory", "tests/oauth2/client_credential/test_oauth2_client_credential.py::test_client_id_is_mandatory", "tests/oauth2/client_credential/test_oauth2_client_credential.py::test_client_secret_is_mandatory", "tests/oauth2/client_credential/test_oauth2_client_credential.py::test_header_value_must_contains_token", "tests/oauth2/client_credential/test_oauth2_client_credential.py::test_token_url_is_mandatory", "tests/oauth2/implicit/azure_ad/test_oauth2_implicit_azure_active_directory.py::test_corresponding_oauth2_implicit_flow_instance", "tests/oauth2/implicit/azure_ad/test_oauth2_implicit_id_token_azure_active_directory.py::test_corresponding_oauth2_implicit_flow_id_token_instance", "tests/oauth2/implicit/okta/test_oauth2_implicit_id_token_okta.py::test_corresponding_oauth2_implicit_flow_id_token_instance", "tests/oauth2/implicit/okta/test_oauth2_implicit_okta.py::test_corresponding_oauth2_implicit_flow_instance", "tests/oauth2/implicit/test_oauth2_implicit.py::test_header_value_must_contains_token", "tests/oauth2/implicit/test_oauth2_implicit.py::test_oauth2_implicit_flow_url_is_mandatory", "tests/oauth2/resource_owner_password/okta/test_oauth2_resource_owner_password_okta.py::test_client_id_is_mandatory", "tests/oauth2/resource_owner_password/okta/test_oauth2_resource_owner_password_okta.py::test_client_secret_is_mandatory", "tests/oauth2/resource_owner_password/okta/test_oauth2_resource_owner_password_okta.py::test_header_value_must_contains_token", "tests/oauth2/resource_owner_password/okta/test_oauth2_resource_owner_password_okta.py::test_instance_is_mandatory", "tests/oauth2/resource_owner_password/okta/test_oauth2_resource_owner_password_okta.py::test_password_is_mandatory", "tests/oauth2/resource_owner_password/okta/test_oauth2_resource_owner_password_okta.py::test_user_name_is_mandatory", "tests/oauth2/resource_owner_password/test_oauth2_resource_owner_password.py::test_header_value_must_contains_token", "tests/oauth2/resource_owner_password/test_oauth2_resource_owner_password.py::test_password_is_mandatory", "tests/oauth2/resource_owner_password/test_oauth2_resource_owner_password.py::test_token_url_is_mandatory", "tests/oauth2/resource_owner_password/test_oauth2_resource_owner_password.py::test_user_name_is_mandatory"]