name: Test

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11', '3.12']

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        python -m pip install .[testing]
    - name: Test with pytest
      run: |
        pytest --doctest-modules --cov=httpx_auth --cov-fail-under=100 --cov-report=term-missing
    - name: Create packages
      run: |
        python -m pip install build
        python -m build .